import { useState, useEffect, useRef } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON>lider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Mic, MicOff, Volume2, VolumeX, Languages, Play, ArrowRight } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface Language {
  code: string;
  name: string;
}

const LANGUAGES: Language[] = [
  { code: "en", name: "English" },
  { code: "zh", name: "Chinese (Mandarin)" },
  { code: "es", name: "Spanish" },
  { code: "fr", name: "French" },
  { code: "de", name: "German" },
  { code: "ja", name: "Japanese" },
  { code: "ko", name: "Korean" },
  { code: "it", name: "Italian" },
  { code: "pt", name: "Portuguese" },
  { code: "ru", name: "Russian" },
];

export default function TranslationApp() {
  const [currentStep, setCurrentStep] = useState(1);
  const [isListening, setIsListening] = useState(false);
  const [inputLanguage, setInputLanguage] = useState("en");
  const [outputLanguage, setOutputLanguage] = useState("zh");
  const [inputText, setInputText] = useState("");
  const [translatedText, setTranslatedText] = useState("");
  const [isTranslating, setIsTranslating] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [liveTextToSpeech, setLiveTextToSpeech] = useState(false);
  const [volume, setVolume] = useState(0.8);
  
  const recognitionRef = useRef<SpeechRecognition | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognitionConstructor = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
      recognitionRef.current = new SpeechRecognitionConstructor();
      
      if (recognitionRef.current) {
        recognitionRef.current.continuous = true;
        recognitionRef.current.interimResults = true;
        recognitionRef.current.lang = inputLanguage;

        recognitionRef.current.onresult = (event: any) => {
          let finalTranscript = '';
          let interimTranscript = '';

          for (let i = event.resultIndex; i < event.results.length; i++) {
            const transcript = event.results[i][0].transcript;
            if (event.results[i].isFinal) {
              finalTranscript += transcript;
            } else {
              interimTranscript += transcript;
            }
          }

          setInputText(finalTranscript + interimTranscript);
          
          if (finalTranscript) {
            translateText(finalTranscript);
          }
        };

        recognitionRef.current.onend = () => {
          setIsListening(false);
        };

        recognitionRef.current.onerror = (event: any) => {
          console.error('Speech recognition error:', event.error);
          setIsListening(false);
          toast({
            title: "Speech Recognition Error",
            description: "Could not access microphone or recognize speech.",
            variant: "destructive",
          });
        };
      }
    } else {
      toast({
        title: "Browser Not Supported",
        description: "Your browser doesn't support speech recognition.",
        variant: "destructive",
      });
    }

    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
    };
  }, [inputLanguage, toast]);

  const toggleListening = async () => {
    if (!recognitionRef.current) return;

    if (isListening) {
      recognitionRef.current.stop();
      setIsListening(false);
    } else {
      try {
        await navigator.mediaDevices.getUserMedia({ audio: true });
        recognitionRef.current.lang = inputLanguage;
        recognitionRef.current.start();
        setIsListening(true);
      } catch (error) {
        toast({
          title: "Microphone Access Denied",
          description: "Please allow microphone access to use speech recognition.",
          variant: "destructive",
        });
      }
    }
  };

  const translateText = async (text: string) => {
    if (!text.trim()) return;
    
    setIsTranslating(true);
    
    // Simulated translation - in a real app, you'd use a translation API
    setTimeout(() => {
      // Simple mock translation
      let translated = "";
      if (outputLanguage === "zh" && inputLanguage === "en") {
        translated = "翻译文本: " + text;
      } else if (outputLanguage === "en" && inputLanguage === "zh") {
        translated = "Translated text: " + text;
      } else {
        translated = `[${outputLanguage.toUpperCase()}] ${text}`;
      }
      
      setTranslatedText(translated);
      setIsTranslating(false);
      
      // Auto-speak if live text-to-speech is enabled
      if (liveTextToSpeech && translated) {
        speakText(translated, outputLanguage);
      }
    }, 1000);
  };

  const speakText = (text: string, language: string) => {
    if (!text || isSpeaking) return;
    
    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = language;
    utterance.rate = 0.9;
    utterance.pitch = 1;
    utterance.volume = volume;
    
    utterance.onstart = () => setIsSpeaking(true);
    utterance.onend = () => setIsSpeaking(false);
    utterance.onerror = () => setIsSpeaking(false);
    
    speechSynthesis.speak(utterance);
  };

  const stopSpeaking = () => {
    speechSynthesis.cancel();
    setIsSpeaking(false);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-translation-surface to-background p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="w-12 h-12 bg-gradient-primary rounded-xl flex items-center justify-center shadow-medium">
              <Languages className="w-6 h-6 text-white" />
            </div>
            <h1 className="text-4xl font-bold bg-gradient-primary bg-clip-text text-transparent">
              Real-Time Translator
            </h1>
          </div>
          <p className="text-muted-foreground text-lg">
            {currentStep === 1 ? "Select your languages to begin" : "Speak naturally and get instant translations"}
          </p>
        </div>

        {/* Step Indicator */}
        <div className="flex items-center justify-center gap-4 mb-8">
          <div className={`flex items-center gap-2 px-4 py-2 rounded-full transition-all ${
            currentStep === 1 ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'
          }`}>
            <div className="w-6 h-6 rounded-full bg-current text-background flex items-center justify-center text-sm font-bold">1</div>
            <span className="font-medium">Select Languages</span>
          </div>
          <ArrowRight className="w-5 h-5 text-muted-foreground" />
          <div className={`flex items-center gap-2 px-4 py-2 rounded-full transition-all ${
            currentStep === 2 ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'
          }`}>
            <div className="w-6 h-6 rounded-full bg-current text-background flex items-center justify-center text-sm font-bold">2</div>
            <span className="font-medium">Translate</span>
          </div>
        </div>

        {/* Step 1: Language Selection */}
        {currentStep === 1 && (
          <Card className="mb-6 p-8 shadow-soft border-0 bg-gradient-surface">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <label className="text-lg font-semibold text-foreground">Input Language</label>
                <Select value={inputLanguage} onValueChange={setInputLanguage}>
                  <SelectTrigger className="bg-card border-border/50 h-12 text-lg">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {LANGUAGES.map((lang) => (
                      <SelectItem key={lang.code} value={lang.code}>
                        {lang.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-3">
                <label className="text-lg font-semibold text-foreground">Output Language</label>
                <Select value={outputLanguage} onValueChange={setOutputLanguage}>
                  <SelectTrigger className="bg-card border-border/50 h-12 text-lg">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {LANGUAGES.map((lang) => (
                      <SelectItem key={lang.code} value={lang.code}>
                        {lang.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="flex justify-center mt-8">
              <Button 
                onClick={() => setCurrentStep(2)} 
                size="lg"
                className="px-8"
              >
                <Play className="w-5 h-5 mr-2" />
                Start Translation
              </Button>
            </div>
          </Card>
        )}

        {/* Step 2: Translation Interface */}
        {currentStep === 2 && (
          <div className="space-y-6">
            {/* Audio Controls */}
            <Card className="p-6 shadow-soft border-0 bg-gradient-surface">
              <div className="flex flex-col gap-6">
                {/* Voice Input Controls */}
                <div className="flex items-center justify-between">
                  <div className="space-y-2">
                    <h3 className="text-lg font-semibold text-foreground">Voice Input</h3>
                    <p className="text-sm text-muted-foreground">
                      {inputLanguage.toUpperCase()} → {outputLanguage.toUpperCase()}
                    </p>
                  </div>
                  <Button
                    onClick={toggleListening}
                    variant={isListening ? "destructive" : "default"}
                    size="lg"
                    className={isListening ? "animate-pulse" : ""}
                  >
                    {isListening ? (
                      <>
                        <MicOff className="w-5 h-5 mr-2" />
                        Stop Listening
                      </>
                    ) : (
                      <>
                        <Mic className="w-5 h-5 mr-2" />
                        Start Listening
                      </>
                    )}
                  </Button>
                </div>

                {/* Live Text-to-Speech and Volume Controls */}
                <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between p-4 bg-card rounded-lg border border-border/50">
                  <div className="flex items-center gap-3">
                    <Switch
                      checked={liveTextToSpeech}
                      onCheckedChange={setLiveTextToSpeech}
                      id="live-tts"
                    />
                    <label htmlFor="live-tts" className="font-medium text-foreground">
                      Live Text-to-Speech
                    </label>
                  </div>
                  <div className="flex items-center gap-3 min-w-[200px]">
                    <Volume2 className="w-4 h-4 text-muted-foreground" />
                    <Slider
                      value={[volume]}
                      onValueChange={(value) => setVolume(value[0])}
                      max={1}
                      min={0}
                      step={0.1}
                      className="flex-1"
                    />
                    <span className="text-sm text-muted-foreground min-w-[3ch]">
                      {Math.round(volume * 100)}%
                    </span>
                  </div>
                </div>
              </div>
            </Card>

            {/* Translation Display */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Input Text */}
              <Card className="p-6 shadow-soft border-0 bg-gradient-surface">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-foreground">Original Text</h3>
                  <span className="text-sm text-muted-foreground font-medium">
                    {LANGUAGES.find(l => l.code === inputLanguage)?.name}
                  </span>
                </div>
                <div className="min-h-[120px] p-4 bg-card rounded-lg border border-border/50">
                  <p className="text-foreground">
                    {inputText || (isListening ? "🔴 Listening..." : "Click Start Listening to begin")}
                  </p>
                </div>
              </Card>

              {/* Translated Text */}
              <Card className="p-6 shadow-soft border-0 bg-gradient-surface">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-foreground">Translation</h3>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-muted-foreground font-medium">
                      {LANGUAGES.find(l => l.code === outputLanguage)?.name}
                    </span>
                    {!liveTextToSpeech && (
                      <Button
                        onClick={() => isSpeaking ? stopSpeaking() : speakText(translatedText, outputLanguage)}
                        variant="outline"
                        size="sm"
                        disabled={!translatedText}
                      >
                        {isSpeaking ? (
                          <>
                            <VolumeX className="w-4 h-4 mr-2" />
                            Stop
                          </>
                        ) : (
                          <>
                            <Volume2 className="w-4 h-4 mr-2" />
                            Speak
                          </>
                        )}
                      </Button>
                    )}
                  </div>
                </div>
                <div className="min-h-[120px] p-4 bg-card rounded-lg border border-border/50">
                  <p className="text-foreground">
                    {isTranslating ? (
                      <span className="flex items-center gap-2">
                        <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
                        Translating...
                      </span>
                    ) : (
                      translatedText || "Translation will appear here"
                    )}
                  </p>
                </div>
              </Card>
            </div>

            {/* Back to Language Selection */}
            <div className="flex justify-center">
              <Button 
                onClick={() => setCurrentStep(1)} 
                variant="outline"
              >
                Change Languages
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
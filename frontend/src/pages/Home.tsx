import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Mic, <PERSON> } from "lucide-react";
import { useNavigate } from "react-router-dom";

const Home = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-gradient-surface p-4">
      <div className="container mx-auto max-w-4xl pt-20">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-foreground mb-4">
            Real-Time Translation Platform
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Connect speakers and audiences across language barriers with instant translation
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8 max-w-2xl mx-auto">
          <Card className="group cursor-pointer hover:shadow-strong transition-all duration-smooth border-border/50 hover:border-primary/30">
            <CardHeader className="text-center pb-4">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-primary/20 transition-colors duration-smooth">
                <Mic className="w-8 h-8 text-primary" />
              </div>
              <CardTitle className="text-xl">Join as Speaker</CardTitle>
              <CardDescription className="text-base">
                Create a session and share your voice with your audience
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-0">
              <Button 
                onClick={() => navigate("/speaker")}
                className="w-full bg-gradient-primary hover:opacity-90 text-white font-medium"
                size="lg"
              >
                Start Speaking
              </Button>
            </CardContent>
          </Card>

          <Card className="group cursor-pointer hover:shadow-strong transition-all duration-smooth border-border/50 hover:border-primary/30">
            <CardHeader className="text-center pb-4">
              <div className="w-16 h-16 bg-accent/10 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-accent/20 transition-colors duration-smooth">
                <Users className="w-8 h-8 text-accent" />
              </div>
              <CardTitle className="text-xl">Join as Audience</CardTitle>
              <CardDescription className="text-base">
                Connect to a session and receive real-time translations
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-0">
              <Button 
                onClick={() => navigate("/audience")}
                variant="outline"
                className="w-full border-accent/30 text-accent hover:bg-accent/10"
                size="lg"
              >
                Join Session
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Home;
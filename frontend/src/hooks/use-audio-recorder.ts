import { useRef, useState, useCallback } from 'react';

export interface AudioRecorderOptions {
  sampleRate?: number;
  channels?: number;
  onDataAvailable?: (audioData: ArrayBuffer) => void;
  onError?: (error: string) => void;
}

export interface UseAudioRecorderReturn {
  isRecording: boolean;
  isSupported: boolean;
  error: string | null;
  startRecording: () => Promise<void>;
  stopRecording: () => void;
  pauseRecording: () => void;
  resumeRecording: () => void;
}

export const useAudioRecorder = (
  options: AudioRecorderOptions = {}
): UseAudioRecorderReturn => {
  const {
    sampleRate = 16000,
    channels = 1,
    onDataAvailable,
    onError,
  } = options;

  const [isRecording, setIsRecording] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const processorRef = useRef<ScriptProcessorNode | null>(null);
  const sourceRef = useRef<MediaStreamAudioSourceNode | null>(null);

  const isSupported = typeof navigator !== 'undefined' && 
    'mediaDevices' in navigator && 
    'getUserMedia' in navigator.mediaDevices;

  const startRecording = useCallback(async () => {
    if (!isSupported) {
      const errorMsg = 'Audio recording is not supported in this browser';
      setError(errorMsg);
      onError?.(errorMsg);
      return;
    }

    if (isRecording) return;

    try {
      setError(null);
      
      // Request microphone access
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: sampleRate,
          channelCount: channels,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        }
      });

      streamRef.current = stream;

      // Create audio context for processing
      audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)({
        sampleRate: sampleRate,
      });

      const source = audioContextRef.current.createMediaStreamSource(stream);
      sourceRef.current = source;

      // Create script processor for real-time audio data
      const bufferSize = 4096;
      const processor = audioContextRef.current.createScriptProcessor(bufferSize, channels, channels);
      processorRef.current = processor;

      processorRef.current.onaudioprocess = (event) => {
        if (!isRecording) return;

        const inputBuffer = event.inputBuffer;
        const outputData = new Float32Array(inputBuffer.length * inputBuffer.numberOfChannels);
        
        // Convert to interleaved format
        for (let channel = 0; channel < inputBuffer.numberOfChannels; channel++) {
          const channelData = inputBuffer.getChannelData(channel);
          for (let i = 0; i < channelData.length; i++) {
            outputData[i * inputBuffer.numberOfChannels + channel] = channelData[i];
          }
        }

        // Convert Float32Array to Int16Array (PCM 16-bit)
        const int16Data = new Int16Array(outputData.length);
        for (let i = 0; i < outputData.length; i++) {
          const sample = Math.max(-1, Math.min(1, outputData[i]));
          int16Data[i] = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
        }

        // Send as ArrayBuffer
        if (onDataAvailable) {
          console.log(`[AUDIO] Sending audio data: ${int16Data.buffer.byteLength} bytes`);
          onDataAvailable(int16Data.buffer);
        }
      };

      // Connect the audio processing chain
      source.connect(processorRef.current);
      processorRef.current.connect(audioContextRef.current.destination);

      setIsRecording(true);
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Failed to start recording';
      setError(errorMsg);
      onError?.(errorMsg);
      
      // Clean up on error
      stopRecording();
    }
  }, [isSupported, isRecording, sampleRate, channels, onDataAvailable, onError]);

  const stopRecording = useCallback(() => {
    setIsRecording(false);

    // Clean up audio processing
    if (processorRef.current) {
      processorRef.current.disconnect();
      processorRef.current = null;
    }

    if (sourceRef.current) {
      sourceRef.current.disconnect();
      sourceRef.current = null;
    }

    if (audioContextRef.current) {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }

    // Stop media stream
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
  }, []);

  const pauseRecording = useCallback(() => {
    if (audioContextRef.current && audioContextRef.current.state === 'running') {
      audioContextRef.current.suspend();
    }
  }, []);

  const resumeRecording = useCallback(() => {
    if (audioContextRef.current && audioContextRef.current.state === 'suspended') {
      audioContextRef.current.resume();
    }
  }, []);

  return {
    isRecording,
    isSupported,
    error,
    startRecording,
    stopRecording,
    pauseRecording,
    resumeRecording,
  };
};

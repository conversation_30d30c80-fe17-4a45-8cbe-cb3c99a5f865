import { useEffect, useRef, useState, useCallback } from 'react';

export interface WebSocketMessage {
  type: string;
  data?: any;
}

export interface UseWebSocketOptions {
  onOpen?: () => void;
  onMessage?: (message: WebSocketMessage) => void;
  onError?: (error: Event) => void;
  onClose?: () => void;
  reconnectAttempts?: number;
  reconnectInterval?: number;
}

export interface UseWebSocketReturn {
  socket: WebSocket | null;
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
  sendMessage: (message: string) => void;
  sendBinaryData: (data: ArrayBuffer | Blob) => void;
  connect: () => void;
  disconnect: () => void;
}

export const useWebSocket = (
  url: string | null,
  options: UseWebSocketOptions = {}
): UseWebSocketReturn => {
  const {
    onOpen,
    onMessage,
    onError,
    onClose,
    reconnectAttempts = 3,
    reconnectInterval = 3000,
  } = options;

  const [socket, setSocket] = useState<WebSocket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const reconnectCount = useRef(0);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const connect = useCallback(() => {
    if (!url || isConnecting || isConnected) return;

    setIsConnecting(true);
    setError(null);

    try {
      const ws = new WebSocket(url);

      ws.onopen = () => {
        setIsConnected(true);
        setIsConnecting(false);
        setError(null);
        reconnectCount.current = 0;
        onOpen?.();
      };

      ws.onmessage = (event) => {
        try {
          const message = typeof event.data === 'string' 
            ? JSON.parse(event.data) 
            : { type: 'binary', data: event.data };
          onMessage?.(message);
        } catch (err) {
          // If it's not JSON, treat as plain text
          onMessage?.({ type: 'text', data: event.data });
        }
      };

      ws.onerror = (event) => {
        setError('WebSocket connection error');
        setIsConnecting(false);
        onError?.(event);
      };

      ws.onclose = () => {
        setIsConnected(false);
        setIsConnecting(false);
        setSocket(null);
        onClose?.();

        // Attempt to reconnect if we haven't exceeded the limit
        if (reconnectCount.current < reconnectAttempts) {
          reconnectCount.current++;
          reconnectTimeoutRef.current = setTimeout(() => {
            connect();
          }, reconnectInterval);
        }
      };

      setSocket(ws);
    } catch (err) {
      setError('Failed to create WebSocket connection');
      setIsConnecting(false);
    }
  }, [url, isConnecting, isConnected, onOpen, onMessage, onError, onClose, reconnectAttempts, reconnectInterval]);

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    
    if (socket) {
      socket.close();
    }
    
    setSocket(null);
    setIsConnected(false);
    setIsConnecting(false);
    reconnectCount.current = 0;
  }, [socket]);

  const sendMessage = useCallback((message: string) => {
    if (socket && isConnected) {
      socket.send(message);
    }
  }, [socket, isConnected]);

  const sendBinaryData = useCallback((data: ArrayBuffer | Blob) => {
    if (socket && isConnected) {
      const size = data instanceof ArrayBuffer ? data.byteLength : data.size;
      console.log(`[WEBSOCKET] Sending binary data: ${size} bytes`);
      socket.send(data);
    } else {
      console.log(`[WEBSOCKET] Cannot send binary data - socket: ${!!socket}, connected: ${isConnected}`);
    }
  }, [socket, isConnected]);

  useEffect(() => {
    if (url) {
      connect();
    }

    return () => {
      disconnect();
    };
  }, [url]);

  useEffect(() => {
    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    };
  }, []);

  return {
    socket,
    isConnected,
    isConnecting,
    error,
    sendMessage,
    sendBinaryData,
    connect,
    disconnect,
  };
};

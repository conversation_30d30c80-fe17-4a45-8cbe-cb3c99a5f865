# Live Translation Application

A real-time translation application that allows speakers to broadcast their speech with live transcription and translation to an audience via WebSocket connections.

## Features

- **Speaker Interface**: Record audio and stream it to the backend for transcription
- **Audience Interface**: Join sessions using session codes to receive live transcriptions and translations
- **Real-time Communication**: WebSocket-based communication between frontend and backend
- **Session Management**: Create and join translation sessions with unique codes
- **Audio Processing**: Real-time audio recording and streaming
- **Containerized Deployment**: Docker and Docker Compose support

## Architecture

- **Frontend**: React + TypeScript + Vite with custom WebSocket and audio recording hooks
- **Backend**: FastAPI with WebSocket support for real-time communication
- **Data Storage**: Redis for session management and pub/sub messaging
- **Audio Processing**: Browser-based audio recording with PCM 16-bit encoding

## Quick Start with Docker

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd live-translation
   ```

2. **Set up environment variables (optional)**
   ```bash
   cp .env.example .env
   # Edit .env with your Speechmatics API credentials if you have them
   ```

3. **Start all services**
   ```bash
   docker-compose up --build
   ```

4. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8081
   - Redis: localhost:6379

## Development Setup

### Backend Development

1. **Navigate to backend directory**
   ```bash
   cd backend
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Start Redis (if not using Docker)**
   ```bash
   redis-server
   ```

4. **Run the backend**
   ```bash
   python main.py
   ```

### Frontend Development

1. **Navigate to frontend directory**
   ```bash
   cd frontend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

## Usage

### For Speakers

1. Go to http://localhost:3000
2. Click "Start as Speaker"
3. Configure input and output languages
4. Click "Start Session" to get a session code
5. Share the session code with your audience
6. Click the microphone button to start recording
7. Speak into your microphone - audio will be streamed to the backend

### For Audience

1. Go to http://localhost:3000
2. Click "Join as Audience"
3. Enter the session code provided by the speaker
4. Configure audio settings (volume, auto-play)
5. Click "Join Session"
6. You'll receive live transcriptions and translations

## API Endpoints

- `POST /session/create` - Create a new session
- `POST /session/join/{code}` - Join an existing session
- `GET /session/state/{code}` - Get session state and audience count
- `WS /ws/speaker/{code}` - WebSocket for speakers to send audio data
- `WS /ws/audience/{code}` - WebSocket for audience to receive transcriptions
- `GET /health` - Health check endpoint

## WebSocket Message Format

### Speaker to Backend
- Binary data: Raw PCM 16-bit audio data
- Text message: "END" to signal end of stream

### Backend to Audience
```json
{
  "type": "PartialTranscript",
  "data": {
    "segments": [
      {
        "text": "transcribed text",
        "speaker": "S1",
        "start_time": 0.0,
        "end_time": 2.5
      }
    ]
  }
}
```

## Configuration

### Environment Variables

- `SPEECHMATICS_KEY`: API key for Speechmatics transcription service
- `SM_WS_URL`: WebSocket URL for Speechmatics service
- `REDIS_HOST`: Redis server hostname (default: localhost)
- `REDIS_PORT`: Redis server port (default: 6379)

### Audio Settings

- Sample Rate: 16kHz
- Channels: Mono (1 channel)
- Encoding: PCM 16-bit signed little-endian
- Buffer Size: 4096 samples

## Docker Services

- **redis**: Redis 7 Alpine for session storage
- **backend**: FastAPI application on port 8081
- **frontend**: Nginx-served React app on port 3000

## Health Checks

All services include health checks:
- Redis: `redis-cli ping`
- Backend: HTTP GET to `/health`
- Frontend: HTTP GET to `/health`

## Notes

- The current implementation includes basic WebSocket connectivity and audio streaming
- Transcription and translation features require integration with external services (e.g., Speechmatics)
- For production use, consider adding authentication, rate limiting, and proper error handling
- The frontend includes mock translation for demonstration purposes
